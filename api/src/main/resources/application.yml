# Server Configuration
server:
  port: 8082
  servlet:
    context-path: /loyalty-api


spring:
  profiles:
    active: "dev"

  application:
    name: microservice-template
  # Database Configuration
  jpa:
    database-platform: org.hibernate.dialect.SQLServerDialect
    show-sql: true
    #hibernate:
      #ddl-auto: validate
    properties:
      hibernate:
        format_sql: true

  # Flyway Configuration
  flyway:
    enabled: true
    baseline-on-migrate: true
    locations: classpath:db/migration
    validate-on-migrate: true
  # Jackson Configuration
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: UTC
#  main: [Hussein] we need to make sure if we want to enable it. maybe will be needed with libraries approach
#    allow-bean-definition-overriding: true


# Actuator Configuration
management:
  endpoints:
    web:
      exposure:
        include: '*'
  endpoint:
    health:
      show-details: always
  info:
    env:
      enabled: true

# OpenAPI Configuration
springdoc:
  api-docs:
    path: /api-docs
  swagger-ui:
    path: /swagger-ui.html
    operationsSorter: method

# Logging Configuration
logging:
  level:
    root: DEBUG
    com.example: DEBUG
  structured.format.console: logstash
#  pattern:
#    console: '%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n'


#loyalty system properties
loyalty-api:
  mandatory‑headers:
    - Authorization
    - x-correlation-id
    - x-channel-code
    - x-additional-required‑header
  optional‑headers:
    - x-subchannel-code
    - x-customer-id
    - x-other-optional

loyalty-backend:
  host: "https://loyalty-test.capitalbank.jo:8443"
  headers:
    AcceptLanguage: "en"
    Authorization: "Um9ib3RhY2tfdmlyZ2luXzIwMjA="
    androidOsVersion: "flatter"
    sdkVersion: "1"

