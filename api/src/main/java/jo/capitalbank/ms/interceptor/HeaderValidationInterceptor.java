//package jo.capitalbank.ms.interceptor;
//
//import jakarta.servlet.http.HttpServletRequest;
//import jakarta.servlet.http.HttpServletResponse;
//import jo.capitalbank.ms.library.error.exception.OBBadRequestException;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.stereotype.Component;
//import org.springframework.web.servlet.HandlerInterceptor;
//
//import java.util.Arrays;
//import java.util.List;
//
//@Slf4j
//@Component
//public class HeaderValidationInterceptor implements HandlerInterceptor {
//
//    private static final List<String> MANDATORY_HEADERS = Arrays.asList(
//            "Authorization",
//            "x-channel-code",
//            "Accept"
//    );
//
//    @Override
//    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
//        log.debug("Validating mandatory headers");
//
//        for (String header : MANDATORY_HEADERS) {
//            if (request.getHeader(header) == null || request.getHeader(header).isEmpty()) {
//                log.error("Missing mandatory header: {}", header);
//                throw new OBBadRequestException("Missing mandatory header", header, null, null);
//            }
//        }
//
//        return true;
//    }
//}